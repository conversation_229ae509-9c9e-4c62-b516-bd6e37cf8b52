<?xml version="1.0" encoding="UTF-8"?>
<settings xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
          xsi:schemaLocation="http://maven.apache.org/SETTINGS/1.1.0 http://maven.apache.org/xsd/settings-1.1.0.xsd"
          xmlns="http://maven.apache.org/SETTINGS/1.1.0">
    <servers>
        <server>
            <username>${env.MAVEN_REPO_USER}</username>
            <password>${env.MAVEN_REPO_PASS}</password>
            <id>central</id>
        </server>
        <server>
            <username>${env.MAVEN_REPO_USER}</username>
            <password>${env.MAVEN_REPO_PASS}</password>
            <id>snapshots</id>
        </server>
    </servers>

    <profiles>
        <profile>
            <repositories>
                <repository>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <id>central</id>
                    <name>libs-release</name>
                    <url>https://artifactory.misynergy.com/artifactory/libs-release</url>
                </repository>
                <repository>
                    <snapshots/>
                    <id>snapshots</id>
                    <name>libs-snapshot</name>
                    <url>https://artifactory.misynergy.com/artifactory/libs-snapshot</url>
                </repository>
            </repositories>
            <pluginRepositories>
                <pluginRepository>
                    <snapshots>
                        <enabled>false</enabled>
                    </snapshots>
                    <id>central</id>
                    <name>libs-release</name>
                    <url>https://artifactory.misynergy.com/artifactory/libs-release</url>
                </pluginRepository>
                <pluginRepository>
                    <snapshots/>
                    <id>snapshots</id>
                    <name>libs-snapshot</name>
                    <url>https://artifactory.misynergy.com/artifactory/libs-snapshot</url>
                </pluginRepository>
            </pluginRepositories>
            <id>artifactory</id>
        </profile>
    </profiles>
    <activeProfiles>
        <activeProfile>artifactory</activeProfile>
    </activeProfiles>

<!--    <proxies>-->
<!--        <proxy>-->
<!--            <id>http</id>-->
<!--            <protocol>http</protocol>-->
<!--            <host>*************</host>-->
<!--            <port>3128</port>-->
<!--            <username>${env.PROXY_USERNAME}</username>-->
<!--            <password>${env.PROXY_PASSWORD}</password>-->
<!--            <nonProxyHosts>192.168.0.*</nonProxyHosts>-->
<!--        </proxy>-->
<!--        <proxy>-->
<!--            <id>https</id>-->
<!--            <protocol>https</protocol>-->
<!--            <host>*************</host>-->
<!--            <port>3128</port>-->
<!--            <username>${env.PROXY_USERNAME}</username>-->
<!--            <password>${env.PROXY_PASSWORD}</password>-->
<!--            <nonProxyHosts>192.168.0.*</nonProxyHosts>-->
<!--        </proxy>-->
<!--    </proxies>-->
</settings>
