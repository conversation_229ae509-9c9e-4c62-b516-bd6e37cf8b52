server:
  port: 8082

spring:
  application:
    name: cms-auth
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: mcms-webapp-qa
            client-secret: 75ON7CCA3o7G38uYwsqoNBr0lMv4XSM6
            scope: openid,profile,email,roles
            redirect-uri: https://hnbuwauth.misynergy.com/login/oauth2/code/keycloak
        provider:
          keycloak:
            issuer-uri: https://hnbuwauth.misynergy.com/realms/hnb-general
            jwk-set-uri: https://hnbuwauth.misynergy.com/realms/hnb-general/protocol/openid-connect/certs

application:
  redis:
    host: redis
    port: 6379
  mcms:
      auth-redirect-uri: https://hnbuwauth.misynergy.com/mcms/auth
      post-logout-redirect-uri: https://hnbuwauth.misynergy.com/
