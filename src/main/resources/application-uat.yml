server:
  port: 8082

spring:
  application:
    name: cms-auth
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: mcms-webapp-client
            client-secret: YuU1IPaToKghkQb9AqFZxAYs8XFfDz5Z
            scope: openid,profile,email,roles
            redirect-uri: https://hnbmcmsauth.hnbgeneral.com/login/oauth2/code/keycloak
        provider:
          keycloak:
            issuer-uri: https://uwmotuat.hnbgeneral.com/realms/hnb-general
            jwk-set-uri: https://uwmotuat.hnbgeneral.com/realms/hnb-general/protocol/openid-connect/certs

application:
  redis:
    host: redis
    port: 6379
  mcms:
    auth-redirect-uri: https://mcmsuat.hnbgeneral.com/mcms/auth
    post-logout-redirect-uri: https://hnbmcmsauth.hnbgeneral.com
