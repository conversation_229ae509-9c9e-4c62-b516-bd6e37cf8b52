server:
  port: 8082

spring:
  application:
    name: cms-auth
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: mcms-webapp
            client-secret: 1e284YqXGyufoY6GPwcxPEGWnwDqlmmo
            scope: openid,profile,email,roles
            redirect-uri: https://hnbuwauth.misynergy.com/login/oauth2/code/keycloak
        provider:
          keycloak:
            issuer-uri: https://hnbuwauth.misynergy.com/realms/hnb-general
            jwk-set-uri: https://hnbuwauth.misynergy.com/realms/hnb-general/protocol/openid-connect/certs

application:
  redis:
    host: localhost
    port: 6379
  mcms:
      auth-redirect-uri: https://hnbuwauth.misynergy.com/mcms/auth
      post-logout-redirect-uri: https://hnbuwauth.misynergy.com/
