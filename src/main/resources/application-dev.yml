
server:
  port: 8082

spring:
  application:
    name: cms-auth
  security:
    oauth2:
      client:
        registration:
          keycloak:
            client-id: hnb-mcms-dev
            client-secret: icI8FQTZrN480RZCDCAReEoa8WkRmyT1
            scope: openid,profile,email,roles
            redirect-uri: http://localhost:8082/login/oauth2/code/keycloak
        provider:
          keycloak:
            issuer-uri: https://hnbuwauth.misynergy.com/realms/hnb-general
            jwk-set-uri: https://hnbuwauth.misynergy.com/realms/hnb-general/protocol/openid-connect/certs

application:
  redis:
    host: localhost
    port: 6379
  mcms:
    auth-redirect-uri: http://localhost:8085/mcms/auth
    post-logout-redirect-uri: http://localhost:8082/
