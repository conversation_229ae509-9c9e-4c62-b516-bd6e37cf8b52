package com.misyn.auth;

import jakarta.annotation.PostConstruct;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

import java.time.Clock;
import java.time.LocalDateTime;
import java.util.TimeZone;


@SpringBootApplication
public class CmsAuthApplication {

	public static void main(String[] args) {
		SpringApplication.run(CmsAuthApplication.class, args);
	}
//	@PostConstruct
	void started() {
		System.out.println("currentTimeMillis: {} "+ LocalDateTime.now());
		System.out.println("System default time zone: {} "+ Clock.systemDefaultZone().getZone());
		TimeZone.setDefault(TimeZone.getTimeZone("Australia/Sydney"));
		System.out.println("System updated time zone: {} "+ Clock.systemDefaultZone().getZone());
		System.out.println("updated currentTimeMillis: {} "+ LocalDateTime.now());

	}

}
