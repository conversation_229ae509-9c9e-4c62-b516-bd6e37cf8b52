package com.misyn.auth.api;

import com.misyn.auth.exception.BadRequestException;
import com.misyn.auth.redis.entities.KeycloakSessionDetails;
import com.misyn.auth.redis.service.KeycloakSessionDetailsService;
import com.misyn.auth.util.Util;
import jakarta.annotation.PostConstruct;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.security.oauth2.client.oidc.web.logout.OidcClientInitiatedLogoutSuccessHandler;
import org.springframework.security.oauth2.client.registration.ClientRegistrationRepository;
import org.springframework.security.oauth2.core.oidc.user.OidcUser;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;

import java.io.IOException;
import java.util.Objects;

@Controller
public class AuthApi {
    private final KeycloakSessionDetailsService keycloakSessionDetailsService;
    private final OidcClientInitiatedLogoutSuccessHandler logoutHandler;
    @Value("${application.mcms.auth-redirect-uri}")
    private String mcmsAuthRedirectUri;
    @Value("${application.mcms.post-logout-redirect-uri}")
    private String postLogoutRedirectUri;

    private final String USER_NAME_KEY = "preferred_username";

    public AuthApi(KeycloakSessionDetailsService keycloakSessionDetailsService, ClientRegistrationRepository clientRegistrationRepository) {
        this.keycloakSessionDetailsService = keycloakSessionDetailsService;
        this.logoutHandler = new OidcClientInitiatedLogoutSuccessHandler(clientRegistrationRepository);

    }

    @PostConstruct
    void init() {
        this.logoutHandler.setPostLogoutRedirectUri(postLogoutRedirectUri);
    }

    @GetMapping("/")
    public String home(@AuthenticationPrincipal OidcUser user, HttpServletRequest request) {
        if (Objects.isNull(user)) {
            throw new BadRequestException("User not found");
        }
        String username = Util.getUsername(user.getClaim(USER_NAME_KEY));
     //   String keycloakUserId = Util.getUsername(user.getClaim("sub"));
        keycloakSessionDetailsService.deleteByUsername(username);
        KeycloakSessionDetails keycloakSessionDetails = new KeycloakSessionDetails();
        keycloakSessionDetails.setUsername(username);
        keycloakSessionDetails.setSessionId(request.getSession().getId());
        keycloakSessionDetails.setToken(user.getIdToken().getTokenValue());
        keycloakSessionDetails.setKeycloakUserId(username);
        keycloakSessionDetailsService.saveKeycloakSessionDetails(keycloakSessionDetails);
        return "redirect:" + mcmsAuthRedirectUri + "?token=" + user.getIdToken().getTokenValue();
    }


    @GetMapping("/api/logout")
    public ResponseEntity<Void> logout(@AuthenticationPrincipal OidcUser user, HttpServletRequest request, HttpServletResponse response, Authentication authentication) {

        if (Objects.nonNull(user)) {
            String username = Util.getUsername(user.getClaim(USER_NAME_KEY));
            keycloakSessionDetailsService.deleteByUsername(username);
        }
        request.getSession().invalidate();
        try {
            logoutHandler.onLogoutSuccess(request, response, authentication);
        } catch (IOException | ServletException e) {
            throw new BadRequestException(e);
        }
        return ResponseEntity.ok().build();
    }


}
