package com.misyn.auth.redis.service.impl;

import com.misyn.auth.redis.entities.KeycloakSessionDetails;
import com.misyn.auth.redis.repository.KeycloakSessionDetailsRepository;
import com.misyn.auth.redis.service.KeycloakSessionDetailsService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Slf4j
@Service
public class KeycloakSessionDetailsServiceImpl implements KeycloakSessionDetailsService {
    private final KeycloakSessionDetailsRepository keycloakSessionDetailsRepository;


    @Override
    public void deleteByUsername(String username) {
        try {
            keycloakSessionDetailsRepository.deleteById(username);
        } catch (Exception e) {
            log.error("Error deleting session details for username: {}", username, e);
        }
    }

    @Override
    public void saveKeycloakSessionDetails(KeycloakSessionDetails keycloakSessionDetails) {

        keycloakSessionDetailsRepository.save(keycloakSessionDetails);
    }

    @Override
    public KeycloakSessionDetails findByUsername(String username) {
        return keycloakSessionDetailsRepository.findById(username).orElse(null);
    }
}
